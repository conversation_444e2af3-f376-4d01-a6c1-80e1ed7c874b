import os
import exifread
from PIL import Image, ImageDraw, ImageFont
from moviepy.editor import ImageClip, concatenate_videoclips, AudioFileClip

# 配置
IMAGE_FOLDER = "images"   # 照片文件夹
MUSIC_FILE = "月亮照山川.mp3"    # 背景音乐
OUTPUT_FILE = "memory_video.mp4"
THEME = "夏日的回忆"       # 视频主题
IMG_DURATION = 3          # 每张图片停留秒数
VIDEO_SIZE = (1080, 1920) # 分辨率(宽, 高)

# 获取照片拍摄时间
def get_photo_date(file_path):
    with open(file_path, 'rb') as f:
        tags = exifread.process_file(f, stop_tag="EXIF DateTimeOriginal", details=False)
    date_taken = tags.get("EXIF DateTimeOriginal")
    if date_taken:
        return str(date_taken)
    return "未知日期"

# 给图片添加文字
def add_text_to_image(image_path, date_text, theme_text):
    img = Image.open(image_path).convert("RGB")
    img = img.resize(VIDEO_SIZE)

    draw = ImageDraw.Draw(img)
    font_theme = ImageFont.truetype("arial.ttf", 60)
    font_date = ImageFont.truetype("arial.ttf", 40)

    # 主题文字
    draw.text((50, 50), theme_text, font=font_theme, fill=(255, 255, 255))
    # 日期文字
    draw.text((50, VIDEO_SIZE[1] - 100), date_text, font=font_date, fill=(255, 255, 255))

    return img

# 主流程
def make_memory_video():
    # 1. 获取图片路径并按时间排序
    images = []
    for file in os.listdir(IMAGE_FOLDER):
        if file.lower().endswith(("jpg", "jpeg", "png")):
            file_path = os.path.join(IMAGE_FOLDER, file)
            date_taken = get_photo_date(file_path)
            images.append((file_path, date_taken))
    images.sort(key=lambda x: x[1])

    # 2. 生成视频片段
    clips = []
    for img_path, date_text in images:
        img_with_text = add_text_to_image(img_path, date_text, THEME)
        temp_path = "temp.jpg"
        img_with_text.save(temp_path)

        clip = ImageClip(temp_path).set_duration(IMG_DURATION).fadein(1).fadeout(1)
        clips.append(clip)

    # 3. 拼接视频
    final_clip = concatenate_videoclips(clips, method="compose")

    # 4. 添加音乐
    if os.path.exists(MUSIC_FILE):
        audio = AudioFileClip(MUSIC_FILE).subclip(0, final_clip.duration)
        final_clip = final_clip.set_audio(audio)

    # 5. 输出视频
    final_clip.write_videofile(OUTPUT_FILE, fps=30)

if __name__ == "__main__":
    make_memory_video()
