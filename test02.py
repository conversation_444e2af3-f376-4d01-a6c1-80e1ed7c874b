import os
import random
import exifread
from PIL import Image, ImageDraw, ImageFont
from moviepy.editor import ImageClip, concatenate_videoclips, AudioFileClip
import numpy as np

# 配置
IMAGE_FOLDER = "images"   # 照片文件夹
MUSIC_FILE = "月亮照山川.mp3"    # 背景音乐
OUTPUT_FILE = "memory_video02.mp4"
THEME = "夏日的回忆"
IMG_DURATION = 4           # 每张图片停留秒数
VIDEO_SIZE = (1080, 1920)  # 竖屏分辨率

# 获取照片拍摄时间
def get_photo_date(file_path):
    with open(file_path, 'rb') as f:
        tags = exifread.process_file(f, stop_tag="EXIF DateTimeOriginal", details=False)
    date_taken = tags.get("EXIF DateTimeOriginal")
    if date_taken:
        return str(date_taken)
    return "未知日期"

# 创建渐变遮罩（黑到透明）
def create_gradient_mask(width, height, start_alpha=200, end_alpha=0):
    gradient = np.zeros((height, width, 4), dtype=np.uint8)
    for y in range(height):
        alpha = int(start_alpha + (end_alpha - start_alpha) * (y / height))
        gradient[y, :, 3] = alpha
    return Image.fromarray(gradient)

# 添加文字+渐变遮罩
def add_text_and_overlay(image_path, date_text, theme_text):
    img = Image.open(image_path).convert("RGBA")
    img = img.resize(VIDEO_SIZE)

    # 底部渐变
    overlay = create_gradient_mask(VIDEO_SIZE[0], 300, start_alpha=220, end_alpha=0)
    img.paste(overlay, (0, VIDEO_SIZE[1] - 300), overlay)

    draw = ImageDraw.Draw(img)
    font_theme = ImageFont.truetype("arial.ttf", 70)
    font_date = ImageFont.truetype("arial.ttf", 45)

    # 柔光白色文字
    text_color = (255, 255, 255, 230)

    # 主题文字
    draw.text((50, VIDEO_SIZE[1] - 250), theme_text, font=font_theme, fill=text_color)
    # 日期文字
    draw.text((50, VIDEO_SIZE[1] - 150), date_text, font=font_date, fill=text_color)

    return img

# Ken Burns 动效
def make_ken_burns_clip(img_path, date_text):
    img_with_text = add_text_and_overlay(img_path, date_text, THEME)
    temp_path = "temp.png"
    img_with_text.save(temp_path)

    clip = ImageClip(temp_path, duration=IMG_DURATION)

    zoom_start = 1.0
    zoom_end = 1.1 + random.uniform(0, 0.05)
    x_move = random.uniform(-0.05, 0.05)
    y_move = random.uniform(-0.05, 0.05)

    def fl_func(get_frame, t):
        zoom = zoom_start + (zoom_end - zoom_start) * (t / IMG_DURATION)
        frame = Image.open(temp_path).convert("RGB")  # 转为RGB，去掉alpha

        w, h = frame.size

        new_w = int(w * zoom)
        new_h = int(h * zoom)
        frame = frame.resize((new_w, new_h), Image.LANCZOS)

        left = int((new_w - w) / 2 - x_move * t * w)
        top = int((new_h - h) / 2 - y_move * t * h)
        frame = frame.crop((left, top, left + w, top + h))

        return np.array(frame)

    kb_clip = clip.fl(fl_func).fadein(1).fadeout(1)
    return kb_clip


# 主流程
def make_memory_video():
    # 1. 获取图片路径并排序
    images = []
    for file in os.listdir(IMAGE_FOLDER):
        if file.lower().endswith(("jpg", "jpeg", "png")):
            file_path = os.path.join(IMAGE_FOLDER, file)
            date_taken = get_photo_date(file_path)
            images.append((file_path, date_taken))
    images.sort(key=lambda x: x[1])

    # 2. 生成 clips
    clips = [make_ken_burns_clip(img_path, date_text) for img_path, date_text in images]

    # 3. 拼接视频
    final_clip = concatenate_videoclips(clips, method="compose")

    # 4. 添加音乐
    if os.path.exists(MUSIC_FILE):
        audio = AudioFileClip(MUSIC_FILE).subclip(0, final_clip.duration)
        final_clip = final_clip.set_audio(audio)

    # 5. 导出视频
    final_clip.write_videofile(OUTPUT_FILE, fps=30)

if __name__ == "__main__":
    make_memory_video()
