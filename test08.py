import os
import random
import librosa
import numpy as np
import exifread
from PIL import Image, ImageDraw, ImageFont
from moviepy.editor import VideoClip, concatenate_videoclips, AudioFileClip

IMAGE_FOLDER = "images"   # 照片文件夹
MUSIC_FILE = "月亮照山川.mp3"    # 背景音乐
OUTPUT_FILE = "memory_video08.mp4"
THEME = "夏日的回忆"
VIDEO_SIZE = (1080, 1920)

# 全局变量存储临时文件路径
temp_files = []

# 获取照片日期
def get_photo_date(file_path):
    try:
        with open(file_path, 'rb') as f:
            tags = exifread.process_file(f, stop_tag="EXIF DateTimeOriginal", details=False)
        date_taken = tags.get("EXIF DateTimeOriginal")
        if date_taken:
            return str(date_taken)
    except:
        pass
    return "未知日期"

# 渐变蒙版
def create_gradient_mask(width, height, start_alpha=200, end_alpha=0):
    gradient = np.zeros((height, width, 4), dtype=np.uint8)
    for y in range(height):
        alpha = int(start_alpha + (end_alpha - start_alpha) * (y / height))
        gradient[y, :, 3] = alpha
    return Image.fromarray(gradient)

# 加文字和遮罩
def add_text_and_overlay(image_path, date_text, theme_text):
    img = Image.open(image_path).convert("RGB")  # 直接转换为RGB
    img = img.resize(VIDEO_SIZE)

    overlay = create_gradient_mask(VIDEO_SIZE[0], 300, start_alpha=220, end_alpha=0)
    # 创建一个RGB背景来合成
    overlay_rgb = Image.new('RGB', VIDEO_SIZE, (0, 0, 0))
    overlay_rgb.paste(overlay, (0, VIDEO_SIZE[1] - 300), overlay)

    # 简单的alpha混合
    img = Image.blend(img, overlay_rgb, 0.3)

    draw = ImageDraw.Draw(img)
    try:
        # 尝试使用系统字体
        font_theme = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 70)  # 微软雅黑
        font_date = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 45)
    except:
        try:
            font_theme = ImageFont.truetype("arial.ttf", 70)
            font_date = ImageFont.truetype("arial.ttf", 45)
        except:
            # 使用默认字体
            font_theme = ImageFont.load_default()
            font_date = ImageFont.load_default()

    text_color = (255, 255, 255)  # RGB格式，不需要alpha
    draw.text((50, VIDEO_SIZE[1] - 250), theme_text, font=font_theme, fill=text_color)
    draw.text((50, VIDEO_SIZE[1] - 150), date_text, font=font_date, fill=text_color)
    return img

# 简化的Ken Burns动效
def make_ken_burns_clip(img_path, date_text, duration, zoom_start, zoom_end):
    print(f"  处理图片: {img_path}")
    img_with_text = add_text_and_overlay(img_path, date_text, THEME)
    temp_path = f"temp_{random.randint(1000, 9999)}.png"
    img_with_text.save(temp_path)
    temp_files.append(temp_path)

    # 预加载图片以减少重复IO
    base_frame = Image.open(temp_path)
    if base_frame.mode != 'RGB':
        base_frame = base_frame.convert('RGB')

    def make_frame(t):
        zoom = zoom_start + (zoom_end - zoom_start) * (t / duration)

        # 使用预加载的图片
        frame = base_frame.copy()
        w, h = frame.size
        new_w = int(w * zoom)
        new_h = int(h * zoom)

        # 只在需要时才进行resize
        if zoom != 1.0:
            frame = frame.resize((new_w, new_h), Image.LANCZOS)
            left = int((new_w - w) / 2)
            top = int((new_h - h) / 2)
            frame = frame.crop((left, top, left + w, top + h))

        return np.array(frame)

    clip = VideoClip(make_frame, duration=duration)
    return clip

# 清理所有临时文件
def cleanup_temp_files():
    for temp_path in temp_files:
        try:
            os.remove(temp_path)
        except:
            pass
    temp_files.clear()

# 提取节拍 & 强弱
def get_beats_with_strength(music_file):
    y, sr = librosa.load(music_file, sr=None)
    tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
    beat_times = librosa.frames_to_time(beats, sr=sr)
    rms = librosa.feature.rms(y=y)[0]
    beat_strengths = [np.mean(rms[max(0, b-2): b+2]) for b in beats]
    return beat_times, beat_strengths

# 主逻辑 - 简化版本
def make_memory_video():
    print("开始制作视频...")
    beat_times, strengths = get_beats_with_strength(MUSIC_FILE)
    threshold = np.median(strengths)
    print(f"强拍阈值: {threshold:.4f}")

    images = []
    for file in os.listdir(IMAGE_FOLDER):
        if file.lower().endswith(("jpg", "jpeg", "png")):
            file_path = os.path.join(IMAGE_FOLDER, file)
            date_taken = get_photo_date(file_path)
            images.append((file_path, date_taken))
    images.sort(key=lambda x: x[1])
    print(f"找到 {len(images)} 张图片")

    # 限制处理的节拍数量，避免视频过长
    max_clips = min(20, len(beat_times) - 1)  # 最多20个片段
    print(f"制作 {max_clips} 个片段")

    clips = []
    img_index = 0
    current_img = images[0]

    for i in range(max_clips):
        duration = beat_times[i+1] - beat_times[i]
        if strengths[i] >= threshold:
            img_index += 1
            current_img = images[img_index % len(images)]
            zoom_start, zoom_end = 1.0, 1.08  # 减少缩放范围
        else:
            zoom_start, zoom_end = 1.0, 1.03  # 更小的缩放

        print(f"制作片段 {i+1}/{max_clips}, 时长: {duration:.2f}秒")
        clip = make_ken_burns_clip(current_img[0], current_img[1], duration, zoom_start, zoom_end)
        clips.append(clip)

    print("拼接视频...")
    final_clip = concatenate_videoclips(clips, method="compose")

    print("添加音乐...")
    audio = AudioFileClip(MUSIC_FILE).subclip(0, final_clip.duration)
    final_clip = final_clip.set_audio(audio)

    print("导出视频...")
    # 降低输出质量以加快渲染速度
    final_clip.write_videofile(OUTPUT_FILE, fps=24, bitrate="2000k")

    # 清理临时文件
    cleanup_temp_files()
    print("完成!")

if __name__ == "__main__":
    make_memory_video()
