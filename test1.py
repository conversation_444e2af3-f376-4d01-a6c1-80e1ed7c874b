import os
import exifread
import random
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from moviepy.editor import ImageClip, concatenate_videoclips, AudioFileClip

# 配置
IMAGE_FOLDER = "images"   # 照片文件夹
MUSIC_FILE = "月亮照山川.mp3"    # 背景音乐
OUTPUT_FILE = "memory_video01.mp4"
THEME = "夏日的回忆"
IMG_DURATION = 4          # 每张图片停留秒数
VIDEO_SIZE = (1080, 1920) # 竖屏分辨率

# 获取照片拍摄时间
def get_photo_date(file_path):
    with open(file_path, 'rb') as f:
        tags = exifread.process_file(f, stop_tag="EXIF DateTimeOriginal", details=False)
    date_taken = tags.get("EXIF DateTimeOriginal")
    if date_taken:
        print(str(date_taken))
        return str(date_taken)
    return "未知日期"

# 添加文字到图片
def add_text_to_image(image_path, date_text, theme_text):
    img = Image.open(image_path).convert("RGB")
    img = img.resize(VIDEO_SIZE)

    draw = ImageDraw.Draw(img)
    font_theme = ImageFont.truetype("arial.ttf", 60)
    font_date = ImageFont.truetype("arial.ttf", 40)

    # 主题文字
    draw.text((50, 50), theme_text, font=font_theme, fill=(255, 255, 255))
    # 日期文字
    draw.text((50, VIDEO_SIZE[1] - 100), date_text, font=font_date, fill=(255, 255, 255))

    return img

# Ken Burns 动效生成 clip
def make_ken_burns_clip(img_path, date_text):
    # 添加文字
    img_with_text = add_text_to_image(img_path, date_text, THEME)
    temp_path = "temp.jpg"
    img_with_text.save(temp_path)

    # 创建 image clip
    clip = ImageClip(temp_path, duration=IMG_DURATION)

    # 随机决定缩放方向
    zoom_start = 1.0
    zoom_end = 1.1 + random.uniform(0, 0.05)  # 轻微放大
    x_move = random.uniform(-0.05, 0.05)      # 左右平移
    y_move = random.uniform(-0.05, 0.05)      # 上下平移

    def zoom_pan(t):
        zoom = zoom_start + (zoom_end - zoom_start) * (t / IMG_DURATION)
        x_offset = x_move * t
        y_offset = y_move * t
        return (zoom, x_offset, y_offset)

    # 这里fl需要的是一个两参数函数
    def fl_func(get_frame, t):
        zoom, x_off, y_off = zoom_pan(t)
        frame = get_frame(t)  # 注意这里用get_frame(t)，不是固定0帧
        frame_img = Image.fromarray(frame)

        w, h = frame_img.size
        new_w = int(w * zoom)
        new_h = int(h * zoom)
        frame_img = frame_img.resize((new_w, new_h), Image.LANCZOS)

        left = int((new_w - w) / 2 - x_off * w)
        top = int((new_h - h) / 2 - y_off * h)
        frame_img = frame_img.crop((left, top, left + w, top + h))

        return np.array(frame_img)

    kb_clip = clip.fl(fl_func).fadein(1).fadeout(1)
    return kb_clip


# 主流程
def make_memory_video():
    # 1. 获取图片路径并按时间排序
    images = []
    for file in os.listdir(IMAGE_FOLDER):
        if file.lower().endswith(("jpg", "jpeg", "png")):
            file_path = os.path.join(IMAGE_FOLDER, file)
            date_taken = get_photo_date(file_path)
            images.append((file_path, date_taken))
    images.sort(key=lambda x: x[1])

    # 2. 生成 Ken Burns clips
    clips = [make_ken_burns_clip(img_path, date_text) for img_path, date_text in images]

    # 3. 拼接视频
    final_clip = concatenate_videoclips(clips, method="compose")

    # 4. 添加音乐
    if os.path.exists(MUSIC_FILE):
        audio = AudioFileClip(MUSIC_FILE).subclip(0, final_clip.duration)
        final_clip = final_clip.set_audio(audio)

    # 5. 输出视频
    final_clip.write_videofile(OUTPUT_FILE, fps=30)

if __name__ == "__main__":
    make_memory_video()
