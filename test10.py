import os
import random
import librosa
import numpy as np
import exifread
from PIL import Image, ImageDraw, ImageFont
from moviepy.editor import (
    ImageClip, concatenate_videoclips, AudioFileClip, TextClip, vfx
)

IMAGE_FOLDER = "images"   # 照片文件夹
MUSIC_FILE = "月亮照山川.mp3"    # 背景音乐
OUTPUT_FILE = "memory_video_beat_aligned.mp4"
THEME = "夏日的回忆"
VIDEO_SIZE = (1080, 1920)
CROSSFADE_TIME = 0.3  # 快速切换过渡时长

def get_photo_date(file_path):
    with open(file_path, 'rb') as f:
        tags = exifread.process_file(f, stop_tag="EXIF DateTimeOriginal", details=False)
    date_taken = tags.get("EXIF DateTimeOriginal")
    return str(date_taken) if date_taken else "未知日期"

def create_gradient_mask(width, height, start_alpha=200, end_alpha=0):
    gradient = np.zeros((height, width, 4), dtype=np.uint8)
    for y in range(height):
        alpha = int(start_alpha + (end_alpha - start_alpha) * (y / height))
        gradient[y, :, 3] = alpha
    return Image.fromarray(gradient)

def add_text_and_overlay(image_path, date_text, theme_text):
    img = Image.open(image_path).convert("RGBA").resize(VIDEO_SIZE)
    overlay = create_gradient_mask(VIDEO_SIZE[0], 300, start_alpha=220, end_alpha=0)
    img.paste(overlay, (0, VIDEO_SIZE[1] - 300), overlay)

    draw = ImageDraw.Draw(img)
    font_theme = ImageFont.truetype("arial.ttf", 70)
    font_date = ImageFont.truetype("arial.ttf", 45)
    text_color = (255, 255, 255, 230)
    draw.text((50, VIDEO_SIZE[1] - 250), theme_text, font=font_theme, fill=text_color)
    draw.text((50, VIDEO_SIZE[1] - 150), date_text, font=font_date, fill=text_color)
    return img

def make_ken_burns_pan_clip(img_path, date_text, duration, zoom_start, zoom_end):
    img_with_text = add_text_and_overlay(img_path, date_text, THEME)
    temp_path = "temp.png"
    img_with_text.save(temp_path)

    clip = ImageClip(temp_path, duration=duration).resize(VIDEO_SIZE)

    pan_direction = random.choice(["left", "right", "up", "down"])

    def zoom_and_pan(t):
        zoom = zoom_start + (zoom_end - zoom_start) * (t / duration)
        x_offset, y_offset = 0, 0
        move_amount = 50
        if pan_direction == "left":
            x_offset = -move_amount * (t / duration)
        elif pan_direction == "right":
            x_offset = move_amount * (t / duration)
        elif pan_direction == "up":
            y_offset = -move_amount * (t / duration)
        elif pan_direction == "down":
            y_offset = move_amount * (t / duration)
        return clip.resize(zoom).set_position((x_offset, y_offset))

    return clip.fl_time(lambda t: t).fl(zoom_and_pan)

def get_beats_and_strengths(music_file):
    y, sr = librosa.load(music_file, sr=None)
    onset_env = librosa.onset.onset_strength(y=y, sr=sr)
    tempo, beats = librosa.beat.beat_track(onset_envelope=onset_env, sr=sr)
    beat_times = librosa.frames_to_time(beats, sr=sr)
    strengths = onset_env[beats]
    return beat_times, strengths

def make_theme_intro(text, duration=2):
    txt_clip = TextClip(
        text, fontsize=100, font="Arial-Bold", color="white", size=VIDEO_SIZE, method="label"
    ).set_duration(duration).fadein(1).fadeout(1)
    return txt_clip

def make_memory_video():
    beat_times, strengths = get_beats_and_strengths(MUSIC_FILE)
    threshold = np.percentile(strengths, 70)

    images = []
    for f in os.listdir(IMAGE_FOLDER):
        if f.lower().endswith(("jpg", "jpeg", "png")):
            path = os.path.join(IMAGE_FOLDER, f)
            date = get_photo_date(path)
            images.append((path, date))
    images.sort(key=lambda x: x[1])

    clips = []
    img_idx = 0
    current_img = images[0]

    # 这里确保切换时长与节拍间隔完全对齐
    for i in range(len(beat_times) - 1):
        duration = beat_times[i+1] - beat_times[i]
        if strengths[i] >= threshold:
            img_idx += 1
            current_img = images[img_idx % len(images)]
            zoom_start, zoom_end = 1.0, 1.15
        else:
            zoom_start, zoom_end = 1.0, 1.05

        clip = make_ken_burns_pan_clip(current_img[0], current_img[1], duration, zoom_start, zoom_end)
        clips.append(clip)

    # 交叉淡入淡出快速切换
    final_clip = clips[0]
    for next_clip in clips[1:]:
        final_clip = concatenate_videoclips([final_clip, next_clip.crossfadein(CROSSFADE_TIME)], method="compose", padding=-CROSSFADE_TIME)

    intro_clip = make_theme_intro(THEME, duration=3)
    final_clip = concatenate_videoclips([intro_clip, final_clip], method="compose")

    audio = AudioFileClip(MUSIC_FILE).subclip(0, final_clip.duration).audio_fadeout(2)
    final_clip = final_clip.set_audio(audio)

    final_clip.write_videofile(OUTPUT_FILE, fps=30)

if __name__ == "__main__":
    make_memory_video()
